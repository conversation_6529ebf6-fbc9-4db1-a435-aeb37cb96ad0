<template>
  <div class="average-container">
    <SubTitle title="平均行驶里程统计"></SubTitle>
    <el-row>
      <el-col class="mt-10">
        <el-select v-model="typeValue" placeholder="请选择司机类型" class="w200">
          <el-option
            v-for="(item, index) in typeList"
            :key="index"
            :label="item"
            :value="index"
            clearable
            filterable
          ></el-option>
        </el-select>
      </el-col>
      <el-col class="mt-10">
        <TimeScreen></TimeScreen>
      </el-col>
    </el-row>
    <div class="chart-box">
      <div class="chart" id="average-chart"></div>
    </div>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import * as echarts from "echarts";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        typeList: ["全体司机", "大型床位司机", "小型床位司机", "小诊所司机"],
        typeValue: 0,
        chartInstance: null, // 存储图表实例
      };
    },
    mounted() {
      this.initChart();
      // 监听窗口大小变化
      window.addEventListener("resize", this.handleResize);
    },
    beforeDestroy() {
      // 组件销毁前移除事件监听器和销毁图表实例
      window.removeEventListener("resize", this.handleResize);
      if (this.chartInstance) {
        this.chartInstance.dispose();
        this.chartInstance = null;
      }
    },
    methods: {
      initChart() {
        // 获取图表容器
        const chartDom = document.getElementById("average-chart");
        if (!chartDom) return;

        // 初始化图表实例
        this.chartInstance = echarts.init(chartDom);

        // 配置图表选项
        const option = {
          title: {
            text: "平均行驶里程统计",
            left: "center",
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
            },
          },
          legend: {
            data: ["平均里程"],
            top: 30,
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
          },
          yAxis: {
            type: "value",
            name: "里程(km)",
          },
          series: [
            {
              name: "平均里程",
              type: "line",
              data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330],
              smooth: true,
              lineStyle: {
                width: 3,
              },
              itemStyle: {
                color: "#409EFF",
              },
            },
          ],
        };

        // 设置图表配置
        this.chartInstance.setOption(option);
      },

      // 处理窗口大小变化
      handleResize() {
        if (this.chartInstance) {
          // 使用防抖优化性能
          clearTimeout(this.resizeTimer);
          this.resizeTimer = setTimeout(() => {
            this.chartInstance.resize();
          }, 100);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .w200 {
    width: 200px;
  }
  .mt-12 {
    margin-top: 10px;
  }
  .average-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .chart-box {
    width: 100%;
    height: 300px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
